#!/bin/bash

# GitLab Issue Batch CVSS Comment Script
# This script reads IDs from column A of a CSV file, fetches CVSS vectors from GitLab issues,
# and adds combined CVSS comments with environmental metrics

# Check if CSV file is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <csv_file>"
    echo "Example: $0 issues.csv"
    exit 1
fi

CSV_FILE="$1"

# Check if file exists
if [ ! -f "$CSV_FILE" ]; then
    echo "Error: File '$CSV_FILE' not found!"
    exit 1
fi

# Convert Windows line endings to Unix format
echo "Converting file format (removing Windows line endings)..."
dos2unix "$CSV_FILE" 2>/dev/null || {
    echo "Note: dos2unix not found, continuing without conversion"
}

# Repository configuration
REPO="threatalert/axonius-federal"

# Environmental score metrics to append
environmental_score_metrics="MAV:N/MAC:H/MPR:H/MUI:R/MS:X/MC:N/MI:N/MA:L"

echo "Processing CSV file: $CSV_FILE"
echo "Repository: $REPO"
echo "Environmental metrics: $environmental_score_metrics"
echo "----------------------------------------"

# Counter files for tracking across subshell
temp_dir=$(mktemp -d)
processed_file="$temp_dir/processed"
errors_file="$temp_dir/errors"
skipped_file="$temp_dir/skipped"
echo "0" > "$processed_file"
echo "0" > "$errors_file"
echo "0" > "$skipped_file"

# Function to extract NVD V3Vector from issue description
extract_nvd_vector() {
    local issue_id="$1"
    local issue_content
    local debug_mode="${DEBUG:-false}"
    
    # Get the issue details
    issue_content=$(glab issue view "$issue_id" --repo "$REPO" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo "Failed to fetch issue details for #$issue_id" >&2
        return 1
    fi
    
    # Debug: Show raw content if DEBUG is set
    if [ "$debug_mode" = "true" ]; then
        echo "  DEBUG: Raw issue content for #$issue_id:" >&2
        echo "$issue_content" | grep -A5 -B5 "CVSS vectors" >&2
        echo "  DEBUG: Searching for CVSS patterns..." >&2
    fi
    
    # Try multiple patterns to find the NVD V3Vector
    local nvd_vector=""
    
    # Pattern 1: Extract from the specific format we see in GitLab
    # CVSS vectors: {'nvd': {'V2Score': 7.5, 'V3Score': 9.8, 'V2Vector': '...', 'V3Vector': 'CVSS:3.0/...'}}
    nvd_vector=$(echo "$issue_content" | grep "CVSS vectors:" | sed -n "s/.*'V3Vector':[[:space:]]*'\([^']*\)'.*/\1/p")
    
    # Pattern 2: Handle potential formatting variations with double quotes
    if [ -z "$nvd_vector" ]; then
        nvd_vector=$(echo "$issue_content" | grep "CVSS vectors:" | sed -n 's/.*"V3Vector":[[:space:]]*"\([^"]*\)".*/\1/p')
    fi
    
    # Pattern 3: Direct extraction of any CVSS:3.x vector from the CVSS vectors line
    if [ -z "$nvd_vector" ]; then
        nvd_vector=$(echo "$issue_content" | grep "CVSS vectors:" | grep -o "CVSS:3\.[0-9]/[A-Z:/]*" | head -1)
    fi
    
    # Pattern 4: More general CVSS vector extraction
    if [ -z "$nvd_vector" ]; then
        nvd_vector=$(echo "$issue_content" | grep "CVSS vectors:" | grep -o "CVSS:[0-9]\.[0-9]/[A-Z:/]*" | head -1)
    fi
    
    # Pattern 5: Extract any CVSS vector from the entire content as fallback
    if [ -z "$nvd_vector" ]; then
        nvd_vector=$(echo "$issue_content" | grep -o "CVSS:[0-9]\.[0-9]/[A-Z:/]*" | head -1)
    fi
    
    if [ "$debug_mode" = "true" ]; then
        echo "  DEBUG: Found vector: '$nvd_vector'" >&2
    fi
    
    if [ -n "$nvd_vector" ]; then
        echo "$nvd_vector"
        return 0
    else
        return 1
    fi
}

# Read CSV file and extract column A (assuming comma-separated)
# Skip header row if present
tail -n +2 "$CSV_FILE" | while IFS=',' read -r issue_id rest; do
    # Remove quotes, whitespace, and any carriage returns from issue_id
    issue_id=$(echo "$issue_id" | sed 's/^"//;s/"$//' | tr -d '\r\n' | xargs)
    
    # Skip empty IDs
    if [ -z "$issue_id" ] || [ "$issue_id" = "" ]; then
        echo "Skipping empty ID in row"
        continue
    fi
    
    # Check if issue_id is numeric
    if ! [[ "$issue_id" =~ ^[0-9]+$ ]]; then
        echo "Skipping non-numeric ID: '$issue_id'"
        continue
    fi
    
    echo "Processing Issue ID: $issue_id"
    
    # Extract NVD V3Vector from the issue
    echo "  Fetching CVSS vector from issue..."
    nvd_vector=$(extract_nvd_vector "$issue_id")
    
    if [ $? -eq 0 ] && [ -n "$nvd_vector" ]; then
        echo "  Found NVD Vector: $nvd_vector"
        
        # Combine base vector with environmental metrics
        combined_cvss="${nvd_vector}/${environmental_score_metrics}"
        
        # Create comment message with just the combined CVSS
        COMMENT_MESSAGE="$combined_cvss"

        echo "  Combined CVSS: $combined_cvss"
        
        # Add comment to issue
        if glab issue note "$issue_id" -m "$COMMENT_MESSAGE" --repo "$REPO"; then
            echo "  ✅ Successfully added CVSS comment to issue #$issue_id"
            echo $(($(cat "$processed_file") + 1)) > "$processed_file"
        else
            echo "  ❌ Failed to add comment to issue #$issue_id"
            echo $(($(cat "$errors_file") + 1)) > "$errors_file"
        fi
    else
        echo "  ⚠️  No NVD CVSS vector found in issue #$issue_id, skipping..."
        echo $(($(cat "$skipped_file") + 1)) > "$skipped_file"
    fi
    
    echo "----------------------------------------"
    
    # Add delay to avoid overwhelming the server
    sleep 2
done

# Read final counts
processed=$(cat "$processed_file")
errors=$(cat "$errors_file")
skipped=$(cat "$skipped_file")

# Cleanup temp files
rm -rf "$temp_dir"

echo "Processing complete!"
echo "Successfully processed: $processed issues"
echo "Skipped (no CVSS vector found): $skipped issues"
echo "Errors: $errors issues"