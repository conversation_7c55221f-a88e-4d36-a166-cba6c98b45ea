#!/usr/bin/env python3

import sys
import subprocess
import csv
import html
import re
from datetime import datetime

def extract_fixed_version_for_search_string(content, search_string):
    """Extract fixed version information from the table format for a specific search string"""
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        # Check if this line contains our search string (in the Resource column)
        if search_string in line and '|' in line:
            # Split the line by pipes to get table columns
            columns = [col.strip() for col in line.split('|')]
            
            # Typically: | Resource | Detail | First Seen At |
            if len(columns) >= 3:
                resource_col = columns[1] if len(columns) > 1 else ""
                detail_col = columns[2] if len(columns) > 2 else ""
                
                # Verify this row is for our EXACT search string (not partial match)
                if search_string == resource_col.strip():
                    # Look for "Fixed version:" in the detail column
                    if 'fixed version:' in detail_col.lower():
                        # Extract the fixed version
                        detail_parts = detail_col.split('<br/>')
                        for part in detail_parts:
                            if 'fixed version:' in part.lower():
                                # Extract everything after "Fixed version:"
                                idx = part.lower().find('fixed version:')
                                if idx != -1:
                                    fixed_info = part[idx + len('fixed version:'):].strip()
                                    if fixed_info:
                                        # Remove HTML tags and clean up
                                        fixed_info = re.sub(r'<[^>]+>', '', fixed_info)
                                        fixed_info = re.sub(r'\s+', ' ', fixed_info).strip()
                                        return fixed_info
    return ""

def load_search_strings(filename):
    """Load search strings from a file, one per line"""
    with open(filename, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def run_glab_command(issue_id, repo):
    """Run glab command and return output"""
    try:
        result = subprocess.run([
            'glab', 'issue', 'view', str(issue_id), 
            '--comments', '--repo', repo
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if result.returncode != 0:
            return ""
        
        return result.stdout
    except Exception:
        return ""

def extract_threatalert_data(content):
    """Extract structured data from the Threatalert Container Scanner Finding section"""
    lines = content.split('\n')
    start_extracting = False
    data_lines = []
    
    for i, line in enumerate(lines):
        # Look for the start header
        if 'Threatalert Container Scanner Finding' in line:
            # Find the end of this HTML comment block
            for j in range(i, min(len(lines), i+10)):
                if '-->' in lines[j]:
                    start_extracting = True
                    break
            continue
        
        # Look for the end header or start of next comment
        if start_extracting and ('Nessus finding handling' in line or line.strip().startswith('<!--')):
            break
        
        # If we're in the extraction zone, collect the lines
        if start_extracting:
            data_lines.append(line)
    
    # Parse the extracted data into structured fields
    if not data_lines:
        return {}
    
    # Clean up the data - remove HTML comment remnants and decode HTML entities
    clean_lines = []
    for line in data_lines:
        # Skip lines that are just comment remnants
        if line.strip() in ['', '---', '----', '<!--', '-->', '--->']:
            continue
        if line.strip().startswith('----') and len(line.strip()) > 10:
            continue
        clean_lines.append(line)
    
    # Remove leading/trailing empty lines
    while clean_lines and clean_lines[0].strip() == '':
        clean_lines.pop(0)
    while clean_lines and clean_lines[-1].strip() == '':
        clean_lines.pop()
    
    full_text = '\n'.join(clean_lines)
    
    # Decode HTML entities
    full_text = html.unescape(full_text)
    
    # Initialize result dictionary
    result = {
        'name': '',
        'scope': '',
        'affected_software': '',
        'first_detected': '',
        'scanner': '',
        'scanner_severity': '',
        'cvss_v3_score': '',
        'cvss_vectors': '',
        'originally_published': '',
        'last_modified': '',
        'description': '',
        'references': '',
        'full_text': full_text.strip()
    }
    
    # Extract each field using simple string matching
    for line in clean_lines:
        line = line.strip()
        line = html.unescape(line)  # Decode HTML entities in each line
        
        if line.startswith('**Name:**'):
            result['name'] = line.replace('**Name:**', '').strip()
        elif line.startswith('**Scope:**'):
            result['scope'] = line.replace('**Scope:**', '').strip()
        elif line.startswith('**Affected Software:**'):
            result['affected_software'] = line.replace('**Affected Software:**', '').strip()
        elif line.startswith('**First detected:**'):
            result['first_detected'] = line.replace('**First detected:**', '').strip()
        elif line.startswith('**Scanner:**'):
            result['scanner'] = line.replace('**Scanner:**', '').strip()
        elif line.startswith('**Scanner reported severity:**'):
            result['scanner_severity'] = line.replace('**Scanner reported severity:**', '').strip()
        elif line.startswith('**CVSS V3 score:**'):
            result['cvss_v3_score'] = line.replace('**CVSS V3 score:**', '').strip()
        elif line.startswith('**CVSS vectors:**'):
            result['cvss_vectors'] = line.replace('**CVSS vectors:**', '').strip()
        elif line.startswith('**Originally published:**'):
            result['originally_published'] = line.replace('**Originally published:**', '').strip()
        elif line.startswith('**Vulnerability last modified date:**'):
            result['last_modified'] = line.replace('**Vulnerability last modified date:**', '').strip()
    
    # Extract description (everything between ### Description and ### References)
    desc_start = full_text.find('### Description')
    ref_start = full_text.find('### References')
    if desc_start != -1:
        desc_end = ref_start if ref_start != -1 else len(full_text)
        desc_text = full_text[desc_start:desc_end]
        # Remove the header and clean up
        desc_text = desc_text.replace('### Description', '').strip()
        result['description'] = desc_text
    
    # Extract references (everything after ### References)
    if ref_start != -1:
        ref_text = full_text[ref_start:]
        ref_text = ref_text.replace('### References', '').strip()
        result['references'] = ref_text
    
    return result

def write_csv_results(search_strings, string_results, output_file):
    """Write results to CSV file with structured Threatalert data"""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'search_string', 'issue_id', 'fix_status', 'fixed_version',
            'vuln_name', 'scope', 'affected_software', 'first_detected',
            'scanner', 'scanner_severity', 'cvss_v3_score', 'cvss_vectors',
            'originally_published', 'last_modified', 'description', 'references',
            'full_threatalert_data'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        
        for search_string in search_strings:
            if search_string in string_results:
                # Write Fix Available entries
                for issue_id, _, data, fixed_ver in string_results[search_string]['fix_available']:
                    writer.writerow({
                        'search_string': search_string,
                        'issue_id': issue_id,
                        'fix_status': 'Fix Available',
                        'fixed_version': fixed_ver,
                        'vuln_name': data.get('name', ''),
                        'scope': data.get('scope', ''),
                        'affected_software': data.get('affected_software', ''),
                        'first_detected': data.get('first_detected', ''),
                        'scanner': data.get('scanner', ''),
                        'scanner_severity': data.get('scanner_severity', ''),
                        'cvss_v3_score': data.get('cvss_v3_score', ''),
                        'cvss_vectors': data.get('cvss_vectors', ''),
                        'originally_published': data.get('originally_published', ''),
                        'last_modified': data.get('last_modified', ''),
                        'description': data.get('description', ''),
                        'references': data.get('references', ''),
                        'full_threatalert_data': data.get('full_text', '')
                    })
                
                # Write VD entries
                for issue_id, _, data, fixed_ver in string_results[search_string]['vd']:
                    writer.writerow({
                        'search_string': search_string,
                        'issue_id': issue_id,
                        'fix_status': 'VD',
                        'fixed_version': fixed_ver,
                        'vuln_name': data.get('name', ''),
                        'scope': data.get('scope', ''),
                        'affected_software': data.get('affected_software', ''),
                        'first_detected': data.get('first_detected', ''),
                        'scanner': data.get('scanner', ''),
                        'scanner_severity': data.get('scanner_severity', ''),
                        'cvss_v3_score': data.get('cvss_v3_score', ''),
                        'cvss_vectors': data.get('cvss_vectors', ''),
                        'originally_published': data.get('originally_published', ''),
                        'last_modified': data.get('last_modified', ''),
                        'description': data.get('description', ''),
                        'references': data.get('references', ''),
                        'full_threatalert_data': data.get('full_text', '')
                    })

def main():
    if len(sys.argv) not in [3, 4]:
        print("Usage: python script.py <csv_file> <search_strings_file> [output_csv]")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    search_strings_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) == 4 else f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    repo = "threatalert/axonius-federal"
    
    # Load search strings from file
    search_strings = load_search_strings(search_strings_file)
    print(f"Loaded {len(search_strings)} search strings")
    
    string_results = {}
    
    with open(csv_file, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if not row:
                continue
            
            issue_id = row[0].strip()
            print(f"=== Issue #{issue_id} ===")
            
            # Get issue content once per issue
            issue_content = run_glab_command(issue_id, repo)
            
            if not issue_content:
                print(f"Could not retrieve issue #{issue_id}")
                continue
            
            found_strings = []
            threatalert_data = extract_threatalert_data(issue_content)
            
            # Check each search string - only match if it appears in the resource table
            for search_string in search_strings:
                # First check if this search string appears in a table row
                search_string_in_table = False
                for line in issue_content.split('\n'):
                    line = line.strip()
                    if '|' in line and search_string in line:
                        # Split by pipes to check if it's in the resource column
                        columns = [col.strip() for col in line.split('|')]
                        if len(columns) >= 3:
                            resource_col = columns[1] if len(columns) > 1 else ""
                            if search_string == resource_col.strip():
                                search_string_in_table = True
                                break
                
                if search_string_in_table:
                    found_strings.append(search_string)
                    
                    # Initialize search string tracking
                    if search_string not in string_results:
                        string_results[search_string] = {'fix_available': [], 'vd': []}
                    
                    # Find last matching line for this string (from the table)
                    last_match = ""
                    for line in issue_content.split('\n'):
                        line = line.strip()
                        if '|' in line and search_string in line:
                            columns = [col.strip() for col in line.split('|')]
                            if len(columns) >= 3:
                                resource_col = columns[1] if len(columns) > 1 else ""
                                if search_string == resource_col.strip():
                                    last_match = line
                                    break
                    
                    # Get fixed version specifically for this search string
                    fixed_version = extract_fixed_version_for_search_string(issue_content, search_string)
                    has_fix = bool(fixed_version)
                    
                    # Categorize by fix availability for this specific search string
                    if has_fix:
                        string_results[search_string]['fix_available'].append((issue_id, last_match, threatalert_data, fixed_version))
                        print(f"✓ Found '{search_string}': {last_match} (Fixed: {fixed_version})")
                    else:
                        string_results[search_string]['vd'].append((issue_id, last_match, threatalert_data, fixed_version))
                        print(f"✓ Found '{search_string}': {last_match} (VD - No fix)")
            
            if found_strings:
                fix_count = sum(1 for s in found_strings if extract_fixed_version_for_search_string(issue_content, s))
                vd_count = len(found_strings) - fix_count
                print(f"  → Summary: {fix_count} with fixes, {vd_count} VD")
            else:
                print("✗ No matches found")
            
            print()
    
    # Write results to CSV
    write_csv_results(search_strings, string_results, output_file)
    print(f"\nResults written to: {output_file}")
    
    # Console summary
    print("\n" + "=" * 70)
    print("SUMMARY:")
    print("=" * 70)
    
    total_matches = 0
    strings_with_matches = 0
    
    for search_string in search_strings:
        if search_string in string_results:
            fix_count = len(string_results[search_string]['fix_available'])
            vd_count = len(string_results[search_string]['vd'])
            total_count = fix_count + vd_count
            total_matches += total_count
            strings_with_matches += 1
            
            print(f"'{search_string}': {total_count} matches ({fix_count} fix available, {vd_count} VD)")
        else:
            print(f"'{search_string}': No matches")
    
    print(f"\nTotal matches across all strings: {total_matches}")
    print(f"Search strings with matches: {strings_with_matches}/{len(search_strings)}")

if __name__ == "__main__":
    main()