#!/usr/bin/env python3

import sys
import json
import csv
import re
from datetime import datetime
from collections import Counter

def load_cve_paths(csv_file):
    """Load CVE to path mappings from CSV file"""
    cve_paths = {}
    
    if not csv_file:
        return cve_paths
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            # Check if required columns exist
            if 'path' not in reader.fieldnames:
                print(f"WARNING: 'path' column not found in {csv_file}")
                return cve_paths
            
            # Look for CVE column (try different possible names)
            cve_column = None
            for field in reader.fieldnames:
                if field.lower() in ['cve', 'cve_id', 'cve-id', 'cveid']:
                    cve_column = field
                    break
            
            if not cve_column:
                print(f"WARNING: No CVE column found in {csv_file}. Looked for: cve, cve_id, cve-id, cveid")
                return cve_paths
            
            print(f"Loading CVE paths from {csv_file} (CVE column: '{cve_column}', Path column: 'path')")
            
            for row in reader:
                cve = row.get(cve_column, '').strip()
                path = row.get('path', '').strip()
                
                if cve and path:
                    # Handle multiple CVEs in one cell (comma-separated)
                    cves = [c.strip() for c in cve.split(',')]
                    for single_cve in cves:
                        if single_cve:
                            cve_paths[single_cve.upper()] = path
            
            print(f"Loaded {len(cve_paths)} CVE-to-path mappings")
            
    except FileNotFoundError:
        print(f"WARNING: CVE paths file not found: {csv_file}")
    except Exception as e:
        print(f"ERROR loading CVE paths from {csv_file}: {e}")
    
    return cve_paths

def get_path_for_cve(cve_string, cve_paths):
    """Get file path for a CVE string (which may contain multiple CVEs)"""
    if not cve_string or not cve_paths:
        return ""
    
    # Split multiple CVEs and look up each one
    cves = [c.strip().upper() for c in cve_string.split(',')]
    paths = []
    
    for cve in cves:
        if cve in cve_paths:
            path = cve_paths[cve]
            if path not in paths:  # Avoid duplicates
                paths.append(path)
    
    return '; '.join(paths) if paths else ""

def extract_cve_from_description(description):
    """Extract CVE identifiers from vulnerability description"""
    if not description:
        return ""
    
    # Look for CVE patterns like CVE-2023-1234
    cve_pattern = r'CVE-\d{4}-\d{4,}'
    matches = re.findall(cve_pattern, description, re.IGNORECASE)
    
    if matches:
        # Return the first CVE found, or all of them separated by commas
        return ', '.join(set(matches))  # Use set to remove duplicates
    
    return ""

def determine_package_type(affected_software_str, vuln_name="", description="", references=""):
    """Determine the package manager/ecosystem based on package name patterns, vulnerability name, description, and references"""
    if not affected_software_str:
        return "Unknown"
    
    # Convert all inputs to lowercase for pattern matching
    pkg_str = affected_software_str.lower()
    vuln_str = vuln_name.lower() if vuln_name else ""
    desc_str = description.lower() if description else ""
    ref_str = references.lower() if references else ""
    combined_str = f"{pkg_str} {vuln_str} {desc_str} {ref_str}"
    
    # Enhanced pattern detection with more comprehensive checks
    
    # Seal packages (Python packages hardened by 3rd party provider) - Check first for specificity
    if any(pattern in combined_str for pattern in ['seal', '.sp', 'seal package']):
        return "Seal (Python)"
    
    # Python ecosystem
    python_patterns = [
        'python', 'pip', 'pypi', 'django', 'flask', 'requests', 'urllib3', 'jinja2',
        'setuptools', 'wheel', 'pillow', 'numpy', 'pandas', 'pyyaml', 'cryptography',
        'sqlalchemy', 'psycopg2', 'redis-py', 'celery', 'gunicorn', 'uwsgi',
        'python package', 'pypi.org', 'pip install', '.whl', '.tar.gz',
        'site-packages', '__pycache__', '.pyc'
    ]
    if any(pattern in combined_str for pattern in python_patterns):
        return "Python"
    
    # JavaScript/Node.js ecosystem
    node_patterns = [
        'node', 'npm', 'javascript', 'typescript', 'yarn', 'pnpm', '@types/',
        'angular', 'react', 'vue', 'express', 'lodash', 'moment', 'webpack',
        'babel', 'eslint', 'jest', 'mocha', 'chai', 'socket.io', 'axios',
        'npmjs.com', 'package.json', 'node_modules', '.js', '.ts', '.json'
    ]
    if any(pattern in combined_str for pattern in node_patterns):
        return "Node.js"
    
    # Go ecosystem
    go_patterns = [
        'go', 'golang', 'github.com/', 'gopkg.in/', 'go.mod', 'go.sum',
        'go package', 'go module', 'goproxy.io', 'pkg.go.dev'
    ]
    if any(pattern in combined_str for pattern in go_patterns):
        return "Go"
    
    # Rust ecosystem
    rust_patterns = [
        'rust', 'cargo', 'crate', 'crates.io', 'cargo.toml', 'cargo.lock',
        'rustc', 'rust package', 'rust crate'
    ]
    if any(pattern in combined_str for pattern in rust_patterns):
        return "Rust"
    
    # Java ecosystem
    java_patterns = [
        'java', 'maven', 'gradle', 'spring', 'hibernate', 'junit', 'log4j',
        'jackson', 'apache commons', 'slf4j', 'logback', 'tomcat', 'jetty',
        'mvnrepository.com', 'central.sonatype.com', '.jar', '.war', '.ear',
        'pom.xml', 'build.gradle', 'java package'
    ]
    if any(pattern in combined_str for pattern in java_patterns):
        return "Java"
    
    # .NET ecosystem
    dotnet_patterns = [
        '.net', 'dotnet', 'nuget', 'c#', 'csharp', 'visual basic', 'vb.net',
        'asp.net', 'entity framework', 'newtonsoft', 'microsoft.', 'system.',
        'nuget.org', '.dll', '.exe', 'packages.config', '.csproj', '.vbproj'
    ]
    if any(pattern in combined_str for pattern in dotnet_patterns):
        return ".NET"
    
    # Ruby ecosystem
    ruby_patterns = [
        'ruby', 'gem', 'rubygems', 'rails', 'bundler', 'rake', 'rspec',
        'sinatra', 'devise', 'activerecord', 'rubygems.org', '.gem',
        'gemfile', 'gemfile.lock', 'ruby package'
    ]
    if any(pattern in combined_str for pattern in ruby_patterns):
        return "Ruby"
    
    # PHP ecosystem
    php_patterns = [
        'php', 'composer', 'packagist', 'laravel', 'symfony', 'wordpress',
        'drupal', 'magento', 'phpunit', 'doctrine', 'twig', 'guzzle',
        'packagist.org', 'composer.json', 'composer.lock', '.php'
    ]
    if any(pattern in combined_str for pattern in php_patterns):
        return "PHP"
    
    # Container/Docker ecosystem
    container_patterns = [
        'docker', 'dockerfile', 'container', 'image', 'registry',
        'docker hub', 'quay.io', 'gcr.io', 'docker.io', 'containerization'
    ]
    if any(pattern in combined_str for pattern in container_patterns):
        return "Container"
    
    # Alpine Linux (check before general system libraries)
    alpine_patterns = [
        'alpine', 'musl', 'apk', 'alpine linux', 'pkgs.alpinelinux.org',
        'apk add', 'apk install'
    ]
    if any(pattern in combined_str for pattern in alpine_patterns):
        return "Alpine"
    
    # Debian/Ubuntu (check before general system libraries)
    debian_patterns = [
        '+deb', 'ubuntu', 'debian', 'apt', 'dpkg', 'deb package',
        'packages.debian.org', 'packages.ubuntu.com', '.deb',
        'apt-get', 'apt install'
    ]
    if any(pattern in combined_str for pattern in debian_patterns):
        return "Debian/Ubuntu"
    
    # CentOS/RHEL (check before general system libraries)
    redhat_patterns = [
        '.el7', '.el8', '.el9', 'centos', 'rhel', 'fedora', 'rpm',
        'yum', 'dnf', 'red hat', 'rpmfusion', '.rpm'
    ]
    if any(pattern in combined_str for pattern in redhat_patterns):
        return "CentOS/RHEL"
    
    # System libraries - now try to attribute to specific OS distros
    system_lib_patterns = [
        'libc6', 'glibc', 'libssl', 'openssl', 'zlib', 'libxml2', 'libcurl',
        'libpng', 'libjpeg', 'libfreetype', 'fontconfig', 'expat', 'pcre',
        'ncurses', 'readline', 'libffi', 'libgcc', 'libstdc++', 'ld-linux',
        'linux-vdso', 'libresolv', 'libdl', 'libpthread', 'libm', 'libnss',
        'libgpg-error', 'libgcrypt', 'libsasl2', 'libldap', 'libkrb5'
    ]
    if any(pattern in combined_str for pattern in system_lib_patterns):
        # Try to determine which distro based on additional context
        if any(ctx in combined_str for ctx in ['musl', 'alpine']):
            return "Alpine"
        elif any(ctx in combined_str for ctx in ['glibc', 'libc6', '+deb', 'ubuntu', 'debian']):
            return "Debian/Ubuntu"
        elif any(ctx in combined_str for ctx in ['.el7', '.el8', '.el9', 'centos', 'rhel']):
            return "CentOS/RHEL"
        else:
            # Default to Debian/Ubuntu for glibc-based system libraries (most common)
            return "Debian/Ubuntu"
    
    # Database systems
    database_patterns = [
        'mysql', 'postgresql', 'postgres', 'mariadb', 'sqlite', 'mongodb',
        'redis', 'cassandra', 'elasticsearch', 'couchdb', 'oracle',
        'sql server', 'database', 'db2'
    ]
    if any(pattern in combined_str for pattern in database_patterns):
        return "Database"
    
    # Web servers and proxies
    webserver_patterns = [
        'apache', 'nginx', 'httpd', 'lighttpd', 'caddy', 'traefik',
        'haproxy', 'envoy', 'web server', 'reverse proxy', 'load balancer'
    ]
    if any(pattern in combined_str for pattern in webserver_patterns):
        return "Web Server"
    
    # Security/Crypto libraries
    crypto_patterns = [
        'openssl', 'gnutls', 'mbedtls', 'wolfssl', 'boringssl', 'libressl',
        'cryptography', 'crypto', 'ssl', 'tls', 'certificate', 'x509',
        'rsa', 'aes', 'sha', 'md5', 'hash'
    ]
    if any(pattern in combined_str for pattern in crypto_patterns):
        return "Cryptography"
    
    # Build tools and CI/CD
    build_patterns = [
        'make', 'cmake', 'autoconf', 'automake', 'libtool', 'pkg-config',
        'jenkins', 'gitlab-ci', 'github actions', 'travis', 'circleci',
        'build tool', 'ci/cd', 'continuous integration'
    ]
    if any(pattern in combined_str for pattern in build_patterns):
        return "Build Tools"
    
    # If no specific pattern matches, try to infer from package structure
    if '/' in pkg_str and any(domain in pkg_str for domain in ['github.com', 'gitlab.com', 'bitbucket.org']):
        return "Git Repository"
    
    # Last resort: check for common file extensions in references
    if any(ext in ref_str for ext in ['.py', '.pyc', '.pyo']):
        return "Python"
    elif any(ext in ref_str for ext in ['.js', '.ts', '.json']):
        return "Node.js"
    elif any(ext in ref_str for ext in ['.jar', '.war', '.ear', '.class']):
        return "Java"
    elif any(ext in ref_str for ext in ['.dll', '.exe', '.msi']):
        return ".NET/Windows"
    elif any(ext in ref_str for ext in ['.so', '.a', '.o']):
        # C/C++ compiled libraries are actually system packages managed by OS package managers
        # Try to determine which OS based on context
        if any(ctx in combined_str for ctx in ['musl', 'alpine']):
            return "Alpine"
        elif any(ctx in combined_str for ctx in ['glibc', 'libc6', '+deb', 'ubuntu', 'debian']):
            return "Debian/Ubuntu"
        elif any(ctx in combined_str for ctx in ['.el7', '.el8', '.el9', 'centos', 'rhel']):
            return "CentOS/RHEL"
        else:
            # Default to most common Linux distribution for compiled libraries
            return "Debian/Ubuntu"
    
    return "Other"

def get_base_image_mapping():
    """Return mapping of containers to their base images and team ownership"""
    return {
        # Backend Team Ownership
        'adapter_tunnel_openvpn_exporter': {'base': 'nodes_tunnel_openvpn_exporter_base', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'axonius-base-image': {'base': 'axonius-base-image', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'axonius-manager': {'base': 'axonius-base-image', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'axonius-libs': {'base': 'axonius-base-image', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'node-setup': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'celery': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'core': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'gui': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'api': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'machine-learning': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'network-scanner-adapter': {'base': 'axonius-libs', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'nodes_tunnel': {'base': 'nodes_tunnel', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'syslog-tunnel': {'base': 'axonius/socat', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'node-proxy': {'base': 'axonius/socat', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'tunneler': {'base': 'axonius/socat', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'ldap-tunnel': {'base': 'axonius/socat', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'smtp-tunnel': {'base': 'axonius/socat', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'nodes_tunnel_unbound_exporter': {'base': 'nodes_tunnel_openvpn_exporter_base', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'nodes_tunnel_openvpn_exporter': {'base': 'nodes_tunnel_openvpn_exporter_base', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'openvpn-service': {'base': 'openvpn-service', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'master-proxy': {'base': 'master-proxy', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'vault': {'base': 'vault', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'imagemagick': {'base': 'imagemagick', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'rabbit': {'base': 'rabbit_base_image', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'rabbitmq_fed': {'base': 'rabbit_base_image', 'os': 'Ubuntu 24 (Noble)', 'team': 'Backend'},
        'pdf_next': {'base': 'pdf_next', 'os': 'Debian 12 (Bookworm)', 'team': 'Backend'},
        
        # Services Team Ownership
        'prometheus': {'base': 'prometheus', 'os': 'Ubuntu 24 (Noble)', 'team': 'Services'},
        'mongo': {'base': 'mongo', 'os': 'Ubuntu 24 (Noble)', 'team': 'Services'},
        'configdb': {'base': 'configdb', 'os': 'Ubuntu 24 (Noble)', 'team': 'Services'},
        'redis': {'base': 'redis', 'os': 'Debian 12 (Bookworm)', 'team': 'Services'},
        'cadvisor': {'base': 'cadvisor', 'os': 'Alpine 3.18.6', 'team': 'Services'},
        'otel_collector': {'base': 'otel_collector', 'os': 'Ubuntu 24 (Noble)', 'team': 'Services'},
        'docker_state_exporter': {'base': 'docker_state_exporter', 'os': 'Ubuntu 24 (Noble)', 'team': 'Services'},
        'mongodb_exporter': {'base': 'mongodb_exporter', 'os': 'Ubuntu 22 (Jammy)', 'team': 'Services'},
        'pushgateway': {'base': 'pushgateway', 'os': 'Ubuntu 24 (Noble)', 'team': 'Services'},
        'redis_exporter': {'base': 'redis_exporter', 'os': 'Alpine 3.21', 'team': 'Services'},
        'percona_mongodb_exporter': {'base': 'percona_mongodb_exporter', 'os': 'Ubuntu 22', 'team': 'Services'},
        'node_exporter': {'base': 'node_exporter', 'os': 'Ubuntu 22', 'team': 'Services'},
        'fluentbit': {'base': 'fluentbit', 'os': 'Debian 11', 'team': 'Services'}
    }

def normalize_container_name(search_string):
    """Remove AWS account info from container name"""
    if not search_string:
        return "unknown"
        
    # Remove the AWS account prefix
    aws_prefix = "************.dkr.ecr.us-gov-east-1.amazonaws.com/"
    if search_string.startswith(aws_prefix):
        return search_string[len(aws_prefix):]
    
    return search_string

def create_engineering_report(search_string, issue_id, fix_status, fixed_version, data, package_type="", file_path=""):
    """Create a copy-pastable engineering report for vulnerability"""
    
    # Extract key information
    vuln_name = data.get('name', 'Unknown Vulnerability')
    severity = data.get('scanner_severity', 'Unknown')
    cvss_score = data.get('cvss_v3_score', 'N/A')
    affected_software = data.get('affected_software', 'Unknown')
    description = data.get('description', '').split('\n')[0] if data.get('description') else ""  # First line only
    references = data.get('references', '')
    
    # Extract CVE from description
    cve = extract_cve_from_description(data.get('description', ''))
    
    # Use normalized container name instead of full AWS URL
    container_name = normalize_container_name(search_string)
    
    # Create formatted report
    report = f"""VULNERABILITY REPORT
Container: {container_name}
Issue ID: {issue_id}
Status: {fix_status}
{f'Fixed Version Available: {fixed_version}' if fixed_version else 'No Fix Available (VD)'}

Vulnerability: {vuln_name}
{f'CVE: {cve}' if cve else 'CVE: Not specified'}
Severity: {severity} (CVSS: {cvss_score})
Affected Package: {affected_software}
Package Type: {package_type}
{f'File Path: {file_path}' if file_path else ''}

Description: {description}

{f'References:{chr(10)}{references}' if references else 'References: None available'}

Action Required: {'Update to fixed version' if fixed_version else 'Monitor for future fixes'}"""
    
    return report

def get_severity_score(severity_str):
    """Convert severity string to numeric score for sorting"""
    severity_map = {
        'critical': 5,
        'high': 4,
        'medium': 3,
        'moderate': 3,
        'low': 2,
        'info': 1,
        'informational': 1,
        'unknown': 0
    }
    return severity_map.get(severity_str.lower().strip(), 0)

def parse_cvss_score(cvss_str):
    """Extract numeric CVSS score from string"""
    if not cvss_str:
        return 0.0
    
    # Try to extract number from string like "7.5 (High)" or "7.5"
    match = re.search(r'(\d+\.?\d*)', cvss_str)
    if match:
        try:
            return float(match.group(1))
        except ValueError:
            return 0.0
    return 0.0

def create_normalized_data(all_data):
    """Create normalized dataset with base image grouping"""
    if not all_data:
        print("WARNING: No data provided to create_normalized_data")
        return []
        
    base_image_mapping = get_base_image_mapping()
    normalized_data = []
    
    print(f"Processing {len(all_data)} records for normalization...")
    
    for i, row in enumerate(all_data):
        try:
            # Create a copy of the row
            normalized_row = row.copy()
            
            # Normalize container name
            original_name = row.get('search_string', '')
            if not original_name:
                print(f"WARNING: Row {i} has no search_string")
                continue
                
            container_name = normalize_container_name(original_name)
            normalized_row['container_image'] = container_name
            
            # Add base image information
            if container_name in base_image_mapping:
                mapping = base_image_mapping[container_name]
                normalized_row['base_image'] = mapping['base']
                normalized_row['operating_system'] = mapping['os']
                normalized_row['team_ownership'] = mapping['team']
            else:
                normalized_row['base_image'] = container_name  # fallback
                normalized_row['operating_system'] = 'Unknown'
                normalized_row['team_ownership'] = 'Unknown'
                if i < 10:  # Show first 10 unmapped containers
                    print(f"  No mapping found for: '{container_name}'")
            
            # Remove the original search_string column
            if 'search_string' in normalized_row:
                del normalized_row['search_string']
            
            normalized_data.append(normalized_row)
            
        except Exception as e:
            print(f"ERROR processing row {i}: {e}")
            continue
    
    print(f"Successfully created {len(normalized_data)} normalized records")
    return normalized_data

def write_excel_results(data, output_file, cve_paths=None):
    """Write results to Excel file with multiple sheets"""
    
    # Transform JSON data into tabular format
    all_data = []
    
    for vuln in data['vulnerabilities']:
        # Determine package type
        package_type = determine_package_type(
            vuln['container_package_info'],
            vuln['threatalert_data'].get('name', ''),
            vuln['threatalert_data'].get('description', ''),
            vuln['threatalert_data'].get('references', '')
        )
        
        # Extract CVE and get file path
        cve = extract_cve_from_description(vuln['threatalert_data'].get('description', ''))
        file_path = get_path_for_cve(cve, cve_paths) if cve_paths else ""
        
        # Create engineering report
        eng_report = create_engineering_report(
            vuln['search_string'],
            vuln['issue_id'],
            vuln['fix_status'],
            vuln['fixed_version'],
            vuln['threatalert_data'],
            package_type,
            file_path
        )
        
        # Create data row
        all_data.append({
            'search_string': vuln['search_string'],
            'issue_id': vuln['issue_id'],
            'fix_status': vuln['fix_status'],
            'fixed_version': vuln['fixed_version'],
            'vuln_name': vuln['threatalert_data'].get('name', ''),
            'cve': cve,
            'file_path': file_path,
            'scope': vuln['threatalert_data'].get('scope', ''),
            'affected_software': vuln['container_package_info'],
            'package_type': package_type,
            'first_detected': vuln['threatalert_data'].get('first_detected', ''),
            'scanner': vuln['threatalert_data'].get('scanner', ''),
            'scanner_severity': vuln['threatalert_data'].get('scanner_severity', ''),
            'cvss_v3_score': vuln['threatalert_data'].get('cvss_v3_score', ''),
            'cvss_vectors': vuln['threatalert_data'].get('cvss_vectors', ''),
            'originally_published': vuln['threatalert_data'].get('originally_published', ''),
            'last_modified': vuln['threatalert_data'].get('last_modified', ''),
            'description': vuln['threatalert_data'].get('description', ''),
            'references': vuln['threatalert_data'].get('references', ''),
            'full_threatalert_data': vuln['threatalert_data'].get('full_text', ''),
            'engineering_report': eng_report,
            'severity_score': get_severity_score(vuln['threatalert_data'].get('scanner_severity', '')),
            'cvss_numeric': parse_cvss_score(vuln['threatalert_data'].get('cvss_v3_score', ''))
        })
    
    # Create main DataFrame
    try:
        import pandas as pd
        df = pd.DataFrame(all_data)
    except ImportError:
        print("ERROR: pandas is required but not available. Please install pandas and openpyxl")
        return
    
    if df.empty:
        print("No data to write to Excel file")
        return
    
    # Create Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        # Sheet 1: All Data (sorted by severity)
        df_sorted = df.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
        df_sorted.to_excel(writer, sheet_name='All Vulnerabilities', index=False)
        
        # Sheet 2: All Vulnerabilities Normalized
        print("Creating normalized sheet...")
        normalized_data = create_normalized_data(all_data)
        
        if normalized_data:
            df_normalized = pd.DataFrame(normalized_data)
            
            # Reorder columns to put new ones first
            base_cols = ['container_image', 'base_image', 'operating_system', 'team_ownership']
            other_cols = [col for col in df_normalized.columns if col not in base_cols]
            df_normalized = df_normalized[base_cols + other_cols]
            
            # Sort by team, base image, then severity
            df_normalized_sorted = df_normalized.sort_values(
                ['team_ownership', 'base_image', 'container_image', 'severity_score', 'cvss_numeric'], 
                ascending=[True, True, True, False, False]
            )
            df_normalized_sorted.to_excel(writer, sheet_name='All Vulnerabilities Normalized', index=False)
            print("Normalized sheet written successfully")
        else:
            print("WARNING: No normalized data created!")
        
        # Sheet 3: High Priority (Critical/High severity with fixes)
        high_priority = df[(df['severity_score'] >= 4) & (df['fix_status'] == 'Fix Available')]
        if not high_priority.empty:
            high_priority_sorted = high_priority.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
            high_priority_sorted.to_excel(writer, sheet_name='High Priority Fixes', index=False)
        
        # Sheet 4: Fix Available
        fix_available = df[df['fix_status'] == 'Fix Available']
        if not fix_available.empty:
            fix_available_sorted = fix_available.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
            fix_available_sorted.to_excel(writer, sheet_name='Fix Available', index=False)
        
        # Sheet 5: VD (Vendor Dependency)
        vd_items = df[df['fix_status'] == 'VD']
        if not vd_items.empty:
            vd_sorted = vd_items.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
            vd_sorted.to_excel(writer, sheet_name='VD (No Fix)', index=False)
        
        # Sheet 6: Summary by Container
        container_summary = df.groupby('search_string').agg({
            'issue_id': 'count',
            'fix_status': lambda x: sum(x == 'Fix Available'),
            'severity_score': 'max',
            'cvss_numeric': 'max'
        }).reset_index()
        container_summary.columns = ['Container', 'Total_Vulns', 'Fixes_Available', 'Max_Severity_Score', 'Max_CVSS']
        container_summary['VD_Count'] = container_summary['Total_Vulns'] - container_summary['Fixes_Available']
        container_summary = container_summary.sort_values(['Max_Severity_Score', 'Total_Vulns'], ascending=[False, False])
        container_summary.to_excel(writer, sheet_name='Container Summary', index=False)
        
        # Sheet 7: Summary by Package Type
        package_summary = df.groupby('package_type').agg({
            'issue_id': 'count',
            'fix_status': lambda x: sum(x == 'Fix Available'),
            'severity_score': 'max',
            'cvss_numeric': 'max'
        }).reset_index()
        package_summary.columns = ['Package_Type', 'Total_Vulns', 'Fixes_Available', 'Max_Severity_Score', 'Max_CVSS']
        package_summary['VD_Count'] = package_summary['Total_Vulns'] - package_summary['Fixes_Available']
        package_summary = package_summary.sort_values('Total_Vulns', ascending=False)
        package_summary.to_excel(writer, sheet_name='Package Type Summary', index=False)
        
        # Sheet 8: Severity Distribution
        severity_dist = df['scanner_severity'].value_counts().reset_index()
        severity_dist.columns = ['Severity', 'Count']
        severity_dist.to_excel(writer, sheet_name='Severity Distribution', index=False)
        
        # Sheet 9: Engineering Reports
        reports_df = df[['search_string', 'issue_id', 'fix_status', 'vuln_name', 'engineering_report']].copy()
        reports_df.to_excel(writer, sheet_name='Engineering Reports', index=False)

def write_csv_results(data, output_file, cve_paths=None):
    """Write results to CSV file (fallback when pandas unavailable)"""
    fieldnames = [
        'search_string', 'issue_id', 'fix_status', 'fixed_version',
        'vuln_name', 'cve', 'file_path', 'scope', 'affected_software', 'package_type', 'first_detected',
        'scanner', 'scanner_severity', 'cvss_v3_score', 'cvss_vectors',
        'originally_published', 'last_modified', 'description', 'references',
        'full_threatalert_data', 'engineering_report'
    ]
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for vuln in data['vulnerabilities']:
            # Determine package type
            package_type = determine_package_type(
                vuln['container_package_info'],
                vuln['threatalert_data'].get('name', ''),
                vuln['threatalert_data'].get('description', ''),
                vuln['threatalert_data'].get('references', '')
            )
            
            # Extract CVE and get file path
            cve = extract_cve_from_description(vuln['threatalert_data'].get('description', ''))
            file_path = get_path_for_cve(cve, cve_paths) if cve_paths else ""
            
            # Create engineering report
            eng_report = create_engineering_report(
                vuln['search_string'],
                vuln['issue_id'],
                vuln['fix_status'],
                vuln['fixed_version'],
                vuln['threatalert_data'],
                package_type,
                file_path
            )
            
            writer.writerow({
                'search_string': vuln['search_string'],
                'issue_id': vuln['issue_id'],
                'fix_status': vuln['fix_status'],
                'fixed_version': vuln['fixed_version'],
                'vuln_name': vuln['threatalert_data'].get('name', ''),
                'cve': cve,
                'file_path': file_path,
                'scope': vuln['threatalert_data'].get('scope', ''),
                'affected_software': vuln['container_package_info'],
                'package_type': package_type,
                'first_detected': vuln['threatalert_data'].get('first_detected', ''),
                'scanner': vuln['threatalert_data'].get('scanner', ''),
                'scanner_severity': vuln['threatalert_data'].get('scanner_severity', ''),
                'cvss_v3_score': vuln['threatalert_data'].get('cvss_v3_score', ''),
                'cvss_vectors': vuln['threatalert_data'].get('cvss_vectors', ''),
                'originally_published': vuln['threatalert_data'].get('originally_published', ''),
                'last_modified': vuln['threatalert_data'].get('last_modified', ''),
                'description': vuln['threatalert_data'].get('description', ''),
                'references': vuln['threatalert_data'].get('references', ''),
                'full_threatalert_data': vuln['threatalert_data'].get('full_text', ''),
                'engineering_report': eng_report
            })

def main():
    if len(sys.argv) not in [2, 3, 4]:
        print("Usage: python vulnerability_reporter.py <json_data_file> [cve_paths_csv] [output_file]")
        print("  json_data_file: JSON file from gitlab_data_collector.py")
        print("  cve_paths_csv: Optional CSV file with CVE-to-path mappings (must have 'cve' and 'path' columns)")
        print("  output_file: Optional output filename")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    # Parse arguments - handle optional CVE paths CSV and output file
    cve_paths_csv = None
    output_file = None
    
    if len(sys.argv) >= 3:
        # Check if second argument is CSV file or output file
        arg2 = sys.argv[2]
        if arg2.lower().endswith('.csv'):
            cve_paths_csv = arg2
            if len(sys.argv) == 4:
                output_file = sys.argv[3]
        else:
            output_file = arg2
    
    # Check if pandas is available for Excel output
    try:
        import pandas as pd
        pandas_available = True
        default_ext = "xlsx"
        print("✓ pandas available - will create Excel file with multiple sheets")
    except ImportError:
        pandas_available = False
        default_ext = "csv"
        print("WARNING: pandas not found. Falling back to CSV output.")
        print("For Excel output with multiple sheets, install with: pip install pandas openpyxl")
    
    if not output_file:
        output_file = f"vulnerability_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{default_ext}"
    
    # Load CVE paths if provided
    cve_paths = None
    if cve_paths_csv:
        print(f"Loading CVE paths from: {cve_paths_csv}")
        cve_paths = load_cve_paths(cve_paths_csv)
    else:
        print("No CVE paths file provided - file paths will not be included")
    
    # Load the JSON data
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"ERROR: Could not find data file: {json_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"ERROR: Invalid JSON in data file: {e}")
        sys.exit(1)
    
    print(f"Loaded data collected on: {data['metadata']['collection_date']}")
    print(f"Processing {len(data['vulnerabilities'])} vulnerabilities...")
    
    # Generate reports
    if pandas_available:
        write_excel_results(data, output_file, cve_paths)
        print(f"\nExcel report written to: {output_file}")
        print(f"\nExcel file created with 9 sheets:")
        print("  1. All Vulnerabilities - Complete dataset sorted by severity")
        print("  2. All Vulnerabilities Normalized - Grouped by base images and teams")
        print("  3. High Priority Fixes - Critical/High severity with available fixes")
        print("  4. Fix Available - All vulnerabilities with fixes available")
        print("  5. VD (No Fix) - All vendor dependency issues without fixes")
        print("  6. Container Summary - Statistics per container")
        print("  7. Package Type Summary - Statistics per package ecosystem")
        print("  8. Severity Distribution - Count of vulnerabilities by severity")
        print("  9. Engineering Reports - Ready-to-use vulnerability reports")
    else:
        write_csv_results(data, output_file, cve_paths)
        print(f"\nCSV report written to: {output_file}")
        print("NOTE: For multi-sheet Excel output, install pandas and openpyxl")
    
    # Print summary
    total_vulns = len(data['vulnerabilities'])
    fix_count = sum(1 for v in data['vulnerabilities'] if v['fix_status'] == 'Fix Available')
    vd_count = total_vulns - fix_count
    
    print(f"\nREPORT SUMMARY:")
    print(f"Total vulnerabilities: {total_vulns}")
    print(f"Fix available: {fix_count} ({fix_count/total_vulns*100:.1f}%)")
    print(f"VD (no fix): {vd_count} ({vd_count/total_vulns*100:.1f}%)")
    
    # Show path mapping stats if CVE paths were provided
    if cve_paths:
        vulns_with_paths = 0
        for vuln in data['vulnerabilities']:
            cve = extract_cve_from_description(vuln['threatalert_data'].get('description', ''))
            if get_path_for_cve(cve, cve_paths):
                vulns_with_paths += 1
        
        print(f"Vulnerabilities with file paths: {vulns_with_paths}/{total_vulns} ({vulns_with_paths/total_vulns*100:.1f}%)")
    
    # Show package type distribution
    package_types = Counter()
    for vuln in data['vulnerabilities']:
        package_type = determine_package_type(
            vuln['container_package_info'],
            vuln['threatalert_data'].get('name', ''),
            vuln['threatalert_data'].get('description', ''),
            vuln['threatalert_data'].get('references', '')
        )
        package_types[package_type] += 1
    
    print(f"\nPackage Type Distribution:")
    for pkg_type, count in package_types.most_common():
        print(f"  {pkg_type}: {count} vulnerabilities")

if __name__ == "__main__":
    main()