#!/usr/bin/env python3

import sys
import subprocess
import csv

def run_glab_command(issue_id, repo):
    """Run glab command and return output"""
    try:
        result = subprocess.run([
            'glab', 'issue', 'view', str(issue_id), 
            '--comments', '--repo', repo
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if result.returncode != 0:
            print(f"DEBUG - glab error for issue {issue_id}: {result.stderr}")
            return ""
        
        return result.stdout
    except Exception as e:
        print(f"DEBUG - Exception for issue {issue_id}: {e}")
        return ""

def main():
    if len(sys.argv) != 3:
        print("Usage: python script.py <csv_file> <search_string>")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    search_string = sys.argv[2]
    repo = "threatalert/axonius-federal"
    
    found_issues = []
    fix_available = []
    vd = []
    
    with open(csv_file, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if not row:
                continue
            
            issue_id = row[0].strip()
            print(f"=== Issue #{issue_id} ===")
            
            # Get issue content
            issue_content = run_glab_command(issue_id, repo)
            
            if issue_content and search_string.lower() in issue_content.lower():
                print(f"✓ Found '{search_string}' in issue #{issue_id}")
                
                # Find and display last matching line
                matching_lines = [line for line in issue_content.split('\n') 
                                if search_string.lower() in line.lower()]
                if matching_lines:
                    print(matching_lines[-1])
                
                # Check for fix availability
                if "fixed version:" in issue_content.lower():
                    print("  → Fix Available")
                    fix_available.append(issue_id)
                else:
                    print("  → VD (No fix available)")
                    vd.append(issue_id)
                
                found_issues.append(issue_id)
                print()
            else:
                print(f"✗ Not found in issue #{issue_id}")
    
    # Output results
    print("=" * 50)
    print(f"Total matches: {len(found_issues)}")
    if fix_available:
        print(f"Fix Available: {' '.join(fix_available)}")
    if vd:
        print(f"VD (No fix): {' '.join(vd)}")
    if not found_issues:
        print("No matches found.")

if __name__ == "__main__":
    main()