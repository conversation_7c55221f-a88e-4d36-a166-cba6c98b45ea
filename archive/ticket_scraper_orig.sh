#!/bin/bash


CSV_FILE=$1
SEARCH_STRING=$2
REPO="threatalert/axonius-federal"


# Skip header if present (remove 'tail -n +2' if no header)
tail -n +2 "$CSV_FILE" | while IFS=',' read -r issue_id rest; do
    echo "=== Issue #$issue_id ==="
    
    # Get issue details and comments, search for string
    issue_content=$(glab issue view "$issue_id" --comments --repo $REPO 2>/dev/null)
    
    if echo "$issue_content" | grep -qi "$SEARCH_STRING"; then
        echo "✓ Found '$SEARCH_STRING' in issue #$issue_id"
        # Optionally show the matching lines
        echo "$issue_content" | grep -i --color=always "$SEARCH_STRING"
        echo ""
    else
        echo "✗ Not found in issue #$issue_id"
    fi
done