#!/usr/bin/env python3

import sys
import subprocess
import csv
import html
import re
from datetime import datetime
import pandas as pd
from collections import defaultdict, Counter

def extract_fixed_version_for_search_string(content, search_string):
    """Extract fixed version information from the table format for a specific search string"""
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        # Check if this line contains our search string (in the Resource column)
        if search_string in line and '|' in line:
            # Split the line by pipes to get table columns
            columns = [col.strip() for col in line.split('|')]
            
            # Typically: | Resource | Detail | First Seen At |
            if len(columns) >= 3:
                resource_col = columns[1] if len(columns) > 1 else ""
                detail_col = columns[2] if len(columns) > 2 else ""
                
                # Verify this row is for our EXACT search string (not partial match)
                if search_string == resource_col.strip():
                    # Look for "Fixed version:" in the detail column
                    if 'fixed version:' in detail_col.lower():
                        # Extract the fixed version
                        detail_parts = detail_col.split('<br/>')
                        for part in detail_parts:
                            if 'fixed version:' in part.lower():
                                # Extract everything after "Fixed version:"
                                idx = part.lower().find('fixed version:')
                                if idx != -1:
                                    fixed_info = part[idx + len('fixed version:'):].strip()
                                    if fixed_info:
                                        # Remove HTML tags and clean up
                                        fixed_info = re.sub(r'<[^>]+>', '', fixed_info)
                                        fixed_info = re.sub(r'\s+', ' ', fixed_info).strip()
                                        return fixed_info
    return ""

def extract_container_package_info(content, search_string):
    """Extract container-specific package information from the table row"""
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        
        # Check if this line contains our search string (in the Resource column)
        if search_string in line and '|' in line:
            # Split the line by pipes to get table columns
            columns = [col.strip() for col in line.split('|')]
            
            # Typically: | Resource | Detail | First Seen At |
            if len(columns) >= 3:
                resource_col = columns[1] if len(columns) > 1 else ""
                detail_col = columns[2] if len(columns) > 2 else ""
                
                # Verify this row is for our EXACT search string
                if search_string == resource_col.strip():
                    # Parse the detail column for package info
                    # Format: Package: libc6<br/>Installed version: 2.36-9+deb12u9
                    package_info = {
                        'package_name': '',
                        'installed_version': '',
                        'fixed_version': ''
                    }
                    
                    # Split by <br/> tags
                    detail_parts = detail_col.split('<br/>')
                    
                    for part in detail_parts:
                        part = part.strip()
                        if part.lower().startswith('package:'):
                            package_info['package_name'] = part[len('package:'):].strip()
                        elif 'installed version:' in part.lower():
                            idx = part.lower().find('installed version:')
                            package_info['installed_version'] = part[idx + len('installed version:'):].strip()
                        elif 'fixed version:' in part.lower():
                            idx = part.lower().find('fixed version:')
                            package_info['fixed_version'] = part[idx + len('fixed version:'):].strip()
                    
                    # Format as a readable string
                    result = f"{package_info['package_name']} (installed: {package_info['installed_version']}"
                    if package_info['fixed_version']:
                        result += f", fix available: {package_info['fixed_version']}"
                    result += ")"
                    
                    return result
    
    return ""

def extract_issue_title(content):
    """Extract the issue title from GitLab CLI output"""
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        # Look for the title line which typically starts with the issue number
        if line.startswith('#') and ' ' in line:
            # Extract everything after the issue number
            parts = line.split(' ', 1)
            if len(parts) > 1:
                return parts[1].strip()
    
    return "Unknown Title"

def extract_cve_from_description(description):
    """Extract CVE identifiers from vulnerability description"""
    if not description:
        return ""
    
    # Look for CVE patterns like CVE-2023-1234
    cve_pattern = r'CVE-\d{4}-\d{4,}'
    matches = re.findall(cve_pattern, description, re.IGNORECASE)
    
    if matches:
        # Return the first CVE found, or all of them separated by commas
        return ', '.join(set(matches))  # Use set to remove duplicates
    
    return ""

def load_search_strings(filename):
    """Load search strings from a file, one per line"""
    with open(filename, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def run_glab_command(issue_id, repo):
    """Run glab command and return output"""
    try:
        result = subprocess.run([
            'glab', 'issue', 'view', str(issue_id), 
            '--comments', '--repo', repo
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if result.returncode != 0:
            return ""
        
        return result.stdout
    except Exception:
        return ""

def extract_threatalert_data(content):
    """Extract structured data from the Threatalert Container Scanner Finding section"""
    lines = content.split('\n')
    start_extracting = False
    data_lines = []
    
    for i, line in enumerate(lines):
        # Look for the start header
        if 'Threatalert Container Scanner Finding' in line:
            # Find the end of this HTML comment block
            for j in range(i, min(len(lines), i+10)):
                if '-->' in lines[j]:
                    start_extracting = True
                    break
            continue
        
        # Look for the end header or start of next comment
        if start_extracting and ('Nessus finding handling' in line or line.strip().startswith('<!--')):
            break
        
        # If we're in the extraction zone, collect the lines
        if start_extracting:
            data_lines.append(line)
    
    # Parse the extracted data into structured fields
    if not data_lines:
        return {}
    
    # Clean up the data - remove HTML comment remnants and decode HTML entities
    clean_lines = []
    for line in data_lines:
        # Skip lines that are just comment remnants
        if line.strip() in ['', '---', '----', '<!--', '-->', '--->']:
            continue
        if line.strip().startswith('----') and len(line.strip()) > 10:
            continue
        clean_lines.append(line)
    
    # Remove leading/trailing empty lines
    while clean_lines and clean_lines[0].strip() == '':
        clean_lines.pop(0)
    while clean_lines and clean_lines[-1].strip() == '':
        clean_lines.pop()
    
    full_text = '\n'.join(clean_lines)
    
    # Decode HTML entities
    full_text = html.unescape(full_text)
    
    # Initialize result dictionary
    result = {
        'name': '',
        'scope': '',
        'affected_software': '',
        'first_detected': '',
        'scanner': '',
        'scanner_severity': '',
        'cvss_v3_score': '',
        'cvss_vectors': '',
        'originally_published': '',
        'last_modified': '',
        'description': '',
        'references': '',
        'full_text': full_text.strip()
    }
    
    # Extract each field using simple string matching
    for line in clean_lines:
        line = line.strip()
        line = html.unescape(line)  # Decode HTML entities in each line
        
        if line.startswith('**Name:**'):
            result['name'] = line.replace('**Name:**', '').strip()
        elif line.startswith('**Scope:**'):
            result['scope'] = line.replace('**Scope:**', '').strip()
        elif line.startswith('**Affected Software:**'):
            result['affected_software'] = line.replace('**Affected Software:**', '').strip()
        elif line.startswith('**First detected:**'):
            result['first_detected'] = line.replace('**First detected:**', '').strip()
        elif line.startswith('**Scanner:**'):
            result['scanner'] = line.replace('**Scanner:**', '').strip()
        elif line.startswith('**Scanner reported severity:**'):
            result['scanner_severity'] = line.replace('**Scanner reported severity:**', '').strip()
        elif line.startswith('**CVSS V3 score:**'):
            result['cvss_v3_score'] = line.replace('**CVSS V3 score:**', '').strip()
        elif line.startswith('**CVSS vectors:**'):
            result['cvss_vectors'] = line.replace('**CVSS vectors:**', '').strip()
        elif line.startswith('**Originally published:**'):
            result['originally_published'] = line.replace('**Originally published:**', '').strip()
        elif line.startswith('**Vulnerability last modified date:**'):
            result['last_modified'] = line.replace('**Vulnerability last modified date:**', '').strip()
    
    # Extract description (everything between ### Description and ### References)
    desc_start = full_text.find('### Description')
    ref_start = full_text.find('### References')
    if desc_start != -1:
        desc_end = ref_start if ref_start != -1 else len(full_text)
        desc_text = full_text[desc_start:desc_end]
        # Remove the header and clean up
        desc_text = desc_text.replace('### Description', '').strip()
        result['description'] = desc_text
    
    # Extract references (everything after ### References)
    if ref_start != -1:
        ref_text = full_text[ref_start:]
        ref_text = ref_text.replace('### References', '').strip()
        result['references'] = ref_text
    
    return result

def determine_package_type(affected_software_str, vuln_name=""):
    """Determine the package manager/ecosystem based on package name patterns and vulnerability name"""
    if not affected_software_str:
        return "Unknown"
    
    # Convert to lowercase for pattern matching
    pkg_str = affected_software_str.lower()
    vuln_str = vuln_name.lower() if vuln_name else ""
    combined_str = f"{pkg_str} {vuln_str}"
    
    # Simple pattern checks
    if any(pattern in combined_str for pattern in ['seal', '.sp']):
        return "Seal"
    elif any(pattern in combined_str for pattern in ['python', 'pip', 'pypi', 'django', 'flask']):
        return "Python"
    elif any(pattern in combined_str for pattern in ['rust', 'cargo', 'crate']):
        return "Rust"
    elif any(pattern in combined_str for pattern in ['go', 'golang', 'github.com/', 'gopkg.in/']):
        return "Go"
    elif any(pattern in combined_str for pattern in ['node', 'npm', 'javascript', 'typescript', '@']):
        return "Node"
    elif any(pattern in combined_str for pattern in ['+deb', 'ubuntu', 'debian']):
        return "Debian/Ubuntu"
    elif any(pattern in combined_str for pattern in ['.el7', '.el8', '.el9', 'centos', 'rhel']):
        return "CentOS/RHEL"
    elif any(pattern in combined_str for pattern in ['alpine', 'musl']):
        return "Alpine"
    elif any(pattern in combined_str for pattern in ['libc6', 'glibc', 'libssl', 'openssl']):
        return "Linux (Generic)"
    else:
        return "Other"

def create_engineering_report(search_string, issue_id, fix_status, fixed_version, data, package_type=""):
    """Create a copy-pastable engineering report for vulnerability"""
    
    # Extract key information
    vuln_name = data.get('name', 'Unknown Vulnerability')
    severity = data.get('scanner_severity', 'Unknown')
    cvss_score = data.get('cvss_v3_score', 'N/A')
    affected_software = data.get('affected_software', 'Unknown')
    description = data.get('description', '').split('\n')[0]  # First line only
    references = data.get('references', '')
    
    # Extract CVE from description
    cve = extract_cve_from_description(data.get('description', ''))
    
    # Create formatted report
    report = f"""VULNERABILITY REPORT
Container: {search_string}
Issue ID: {issue_id}
Status: {fix_status}
{f'Fixed Version Available: {fixed_version}' if fixed_version else 'No Fix Available (VD)'}

Vulnerability: {vuln_name}
{f'CVE: {cve}' if cve else 'CVE: Not specified'}
Severity: {severity} (CVSS: {cvss_score})
Affected Package: {affected_software}
Package Type: {package_type}

Description: {description}

{f'References:{chr(10)}{references}' if references else 'References: None available'}

Action Required: {'Update to fixed version' if fixed_version else 'Monitor for future fixes'}"""
    
    return report

def get_severity_score(severity_str):
    """Convert severity string to numeric score for sorting"""
    severity_map = {
        'critical': 5,
        'high': 4,
        'medium': 3,
        'moderate': 3,
        'low': 2,
        'info': 1,
        'informational': 1,
        'unknown': 0
    }
    return severity_map.get(severity_str.lower().strip(), 0)

def parse_cvss_score(cvss_str):
    """Extract numeric CVSS score from string"""
    if not cvss_str:
        return 0.0
    
    # Try to extract number from string like "7.5 (High)" or "7.5"
    import re
    match = re.search(r'(\d+\.?\d*)', cvss_str)
    if match:
        try:
            return float(match.group(1))
        except ValueError:
            return 0.0
    return 0.0

def write_excel_results(search_strings, string_results, output_file):
    """Write results to Excel file with multiple sheets"""
    
    # Collect all data first
    all_data = []
    
    for search_string in search_strings:
        if search_string in string_results:
            # Process Fix Available entries
            for issue_id, _, data, fixed_ver, container_pkg_info in string_results[search_string]['fix_available']:
                package_type = determine_package_type(container_pkg_info, data.get('name', ''))
                eng_report = create_engineering_report(search_string, issue_id, 'Fix Available', fixed_ver, data, package_type)
                cve = extract_cve_from_description(data.get('description', ''))
                
                all_data.append({
                    'search_string': search_string,
                    'issue_id': issue_id,
                    'fix_status': 'Fix Available',
                    'fixed_version': fixed_ver,
                    'vuln_name': data.get('name', ''),
                    'cve': cve,
                    'scope': data.get('scope', ''),
                    'affected_software': container_pkg_info,
                    'package_type': package_type,
                    'first_detected': data.get('first_detected', ''),
                    'scanner': data.get('scanner', ''),
                    'scanner_severity': data.get('scanner_severity', ''),
                    'cvss_v3_score': data.get('cvss_v3_score', ''),
                    'cvss_vectors': data.get('cvss_vectors', ''),
                    'originally_published': data.get('originally_published', ''),
                    'last_modified': data.get('last_modified', ''),
                    'description': data.get('description', ''),
                    'references': data.get('references', ''),
                    'full_threatalert_data': data.get('full_text', ''),
                    'engineering_report': eng_report,
                    'severity_score': get_severity_score(data.get('scanner_severity', '')),
                    'cvss_numeric': parse_cvss_score(data.get('cvss_v3_score', ''))
                })
            
            # Process VD entries
            for issue_id, _, data, fixed_ver, container_pkg_info in string_results[search_string]['vd']:
                package_type = determine_package_type(container_pkg_info, data.get('name', ''))
                eng_report = create_engineering_report(search_string, issue_id, 'VD', fixed_ver, data, package_type)
                cve = extract_cve_from_description(data.get('description', ''))
                
                all_data.append({
                    'search_string': search_string,
                    'issue_id': issue_id,
                    'fix_status': 'VD',
                    'fixed_version': fixed_ver,
                    'vuln_name': data.get('name', ''),
                    'cve': cve,
                    'scope': data.get('scope', ''),
                    'affected_software': container_pkg_info,
                    'package_type': package_type,
                    'first_detected': data.get('first_detected', ''),
                    'scanner': data.get('scanner', ''),
                    'scanner_severity': data.get('scanner_severity', ''),
                    'cvss_v3_score': data.get('cvss_v3_score', ''),
                    'cvss_vectors': data.get('cvss_vectors', ''),
                    'originally_published': data.get('originally_published', ''),
                    'last_modified': data.get('last_modified', ''),
                    'description': data.get('description', ''),
                    'references': data.get('references', ''),
                    'full_threatalert_data': data.get('full_text', ''),
                    'engineering_report': eng_report,
                    'severity_score': get_severity_score(data.get('scanner_severity', '')),
                    'cvss_numeric': parse_cvss_score(data.get('cvss_v3_score', ''))
                })
    
    # Create main DataFrame
    df = pd.DataFrame(all_data)
    
    if df.empty:
        print("No data to write to Excel file")
        return
    
    # Create Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        # Sheet 1: All Data (sorted by severity)
        df_sorted = df.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
        df_sorted.to_excel(writer, sheet_name='All Vulnerabilities', index=False)
        
        # Sheet 2: High Priority (Critical/High severity with fixes)
        high_priority = df[(df['severity_score'] >= 4) & (df['fix_status'] == 'Fix Available')]
        if not high_priority.empty:
            high_priority_sorted = high_priority.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
            high_priority_sorted.to_excel(writer, sheet_name='High Priority Fixes', index=False)
        
        # Sheet 3: Fix Available
        fix_available = df[df['fix_status'] == 'Fix Available']
        if not fix_available.empty:
            fix_available_sorted = fix_available.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
            fix_available_sorted.to_excel(writer, sheet_name='Fix Available', index=False)
        
        # Sheet 4: VD (Vendor Dependency)
        vd_items = df[df['fix_status'] == 'VD']
        if not vd_items.empty:
            vd_sorted = vd_items.sort_values(['severity_score', 'cvss_numeric'], ascending=[False, False])
            vd_sorted.to_excel(writer, sheet_name='VD (No Fix)', index=False)
        
        # Sheet 5: Summary by Container
        container_summary = df.groupby('search_string').agg({
            'issue_id': 'count',
            'fix_status': lambda x: sum(x == 'Fix Available'),
            'severity_score': 'max',
            'cvss_numeric': 'max'
        }).reset_index()
        container_summary.columns = ['Container', 'Total_Vulns', 'Fixes_Available', 'Max_Severity_Score', 'Max_CVSS']
        container_summary['VD_Count'] = container_summary['Total_Vulns'] - container_summary['Fixes_Available']
        container_summary = container_summary.sort_values(['Max_Severity_Score', 'Total_Vulns'], ascending=[False, False])
        container_summary.to_excel(writer, sheet_name='Container Summary', index=False)
        
        # Sheet 6: Summary by Package Type
        if 'package_type' in df.columns:
            package_summary = df.groupby('package_type').agg({
                'issue_id': 'count',
                'fix_status': lambda x: sum(x == 'Fix Available'),
                'severity_score': 'max',
                'cvss_numeric': 'max'
            }).reset_index()
            package_summary.columns = ['Package_Type', 'Total_Vulns', 'Fixes_Available', 'Max_Severity_Score', 'Max_CVSS']
            package_summary['VD_Count'] = package_summary['Total_Vulns'] - package_summary['Fixes_Available']
            package_summary = package_summary.sort_values('Total_Vulns', ascending=False)
            package_summary.to_excel(writer, sheet_name='Package Type Summary', index=False)
        
        # Sheet 7: Severity Distribution
        severity_dist = df['scanner_severity'].value_counts().reset_index()
        severity_dist.columns = ['Severity', 'Count']
        severity_dist.to_excel(writer, sheet_name='Severity Distribution', index=False)
        
        # Sheet 8: Engineering Reports (for easy copy-paste)
        reports_df = df[['search_string', 'issue_id', 'fix_status', 'vuln_name', 'engineering_report']].copy()
        reports_df.to_excel(writer, sheet_name='Engineering Reports', index=False)

def main():
    if len(sys.argv) not in [3, 4]:
        print("Usage: python script.py <csv_file> <search_strings_file> [output_excel]")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    search_strings_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) == 4 else f"vulnerability_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    repo = "threatalert/axonius-federal"
    
    # Ensure pandas is available
    try:
        import pandas as pd
    except ImportError:
        print("ERROR: pandas is required for Excel output. Install with: pip install pandas openpyxl")
        sys.exit(1)
    
    # Load search strings from file
    search_strings = load_search_strings(search_strings_file)
    print(f"Loaded {len(search_strings)} search strings")
    
    string_results = {}
    
    with open(csv_file, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if not row:
                continue
            
            issue_id = row[0].strip()
            print(f"=== Issue #{issue_id} ===")
            
            # Get issue content once per issue
            issue_content = run_glab_command(issue_id, repo)
            
            if not issue_content:
                print(f"Could not retrieve issue #{issue_id}")
                continue
            
            found_strings = []
            threatalert_data = extract_threatalert_data(issue_content)
            issue_title = extract_issue_title(issue_content)
            
            # Check each search string - only match if it appears in the resource table
            for search_string in search_strings:
                # First check if this search string appears in a table row
                search_string_in_table = False
                for line in issue_content.split('\n'):
                    line = line.strip()
                    if '|' in line and search_string in line:
                        # Split by pipes to check if it's in the resource column
                        columns = [col.strip() for col in line.split('|')]
                        if len(columns) >= 3:
                            resource_col = columns[1] if len(columns) > 1 else ""
                            if search_string == resource_col.strip():
                                search_string_in_table = True
                                break
                
                if search_string_in_table:
                    found_strings.append(search_string)
                    
                    # Initialize search string tracking
                    if search_string not in string_results:
                        string_results[search_string] = {'fix_available': [], 'vd': []}
                    
                    # Find last matching line for this string (from the table)
                    last_match = ""
                    for line in issue_content.split('\n'):
                        line = line.strip()
                        if '|' in line and search_string in line:
                            columns = [col.strip() for col in line.split('|')]
                            if len(columns) >= 3:
                                resource_col = columns[1] if len(columns) > 1 else ""
                                if search_string == resource_col.strip():
                                    last_match = line
                                    break
                    
                    # Get container-specific package info and fixed version
                    container_package_info = extract_container_package_info(issue_content, search_string)
                    fixed_version = extract_fixed_version_for_search_string(issue_content, search_string)
                    has_fix = bool(fixed_version)
                    
                    # Categorize by fix availability for this specific search string
                    if has_fix:
                        string_results[search_string]['fix_available'].append((issue_id, last_match, threatalert_data, fixed_version, container_package_info))
                        print(f"✓ Found '{search_string}': {last_match} (Fixed: {fixed_version})")
                    else:
                        string_results[search_string]['vd'].append((issue_id, last_match, threatalert_data, fixed_version, container_package_info))
                        print(f"✓ Found '{search_string}': {last_match} (VD - No fix)")
            
            if found_strings:
                fix_count = sum(1 for s in found_strings if extract_fixed_version_for_search_string(issue_content, s))
                vd_count = len(found_strings) - fix_count
                print(f"  → Summary: {fix_count} with fixes, {vd_count} VD")
            else:
                print("✗ No matches found")
            
            print()
    
    # Write results to Excel with multiple sheets
    write_excel_results(search_strings, string_results, output_file)
    print(f"\nResults written to: {output_file}")
    
    # Console summary
    print("\n" + "=" * 70)
    print("SUMMARY:")
    print("=" * 70)
    
    total_matches = 0
    strings_with_matches = 0
    
    for search_string in search_strings:
        if search_string in string_results:
            fix_count = len(string_results[search_string]['fix_available'])
            vd_count = len(string_results[search_string]['vd'])
            total_count = fix_count + vd_count
            total_matches += total_count
            strings_with_matches += 1
            
            print(f"'{search_string}': {total_count} matches ({fix_count} fix available, {vd_count} VD)")
        else:
            print(f"'{search_string}': No matches")
    
    print(f"\nTotal matches across all strings: {total_matches}")
    print(f"Search strings with matches: {strings_with_matches}/{len(search_strings)}")

def write_csv_results(search_strings, string_results, output_file):
    """Write results to CSV file with structured Threatalert data (fallback when pandas unavailable)"""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'search_string', 'issue_id', 'fix_status', 'fixed_version',
            'vuln_name', 'cve', 'scope', 'affected_software', 'package_type', 'first_detected',
            'scanner', 'scanner_severity', 'cvss_v3_score', 'cvss_vectors',
            'originally_published', 'last_modified', 'description', 'references',
            'full_threatalert_data', 'engineering_report'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        
        for search_string in search_strings:
            if search_string in string_results:
                # Write Fix Available entries
                for issue_id, _, data, fixed_ver, container_pkg_info in string_results[search_string]['fix_available']:
                    package_type = determine_package_type(container_pkg_info, data.get('name', ''))
                    eng_report = create_engineering_report(search_string, issue_id, 'Fix Available', fixed_ver, data, package_type)
                    cve = extract_cve_from_description(data.get('description', ''))
                    
                    writer.writerow({
                        'search_string': search_string,
                        'issue_id': issue_id,
                        'fix_status': 'Fix Available',
                        'fixed_version': fixed_ver,
                        'vuln_name': data.get('name', ''),
                        'cve': cve,
                        'scope': data.get('scope', ''),
                        'affected_software': container_pkg_info,
                        'package_type': package_type,
                        'first_detected': data.get('first_detected', ''),
                        'scanner': data.get('scanner', ''),
                        'scanner_severity': data.get('scanner_severity', ''),
                        'cvss_v3_score': data.get('cvss_v3_score', ''),
                        'cvss_vectors': data.get('cvss_vectors', ''),
                        'originally_published': data.get('originally_published', ''),
                        'last_modified': data.get('last_modified', ''),
                        'description': data.get('description', ''),
                        'references': data.get('references', ''),
                        'full_threatalert_data': data.get('full_text', ''),
                        'engineering_report': eng_report
                    })
                
                # Write VD entries
                for issue_id, _, data, fixed_ver, container_pkg_info in string_results[search_string]['vd']:
                    package_type = determine_package_type(container_pkg_info, data.get('name', ''))
                    eng_report = create_engineering_report(search_string, issue_id, 'VD', fixed_ver, data, package_type)
                    cve = extract_cve_from_description(data.get('description', ''))
                    
                    writer.writerow({
                        'search_string': search_string,
                        'issue_id': issue_id,
                        'fix_status': 'VD',
                        'fixed_version': fixed_ver,
                        'vuln_name': data.get('name', ''),
                        'cve': cve,
                        'scope': data.get('scope', ''),
                        'affected_software': container_pkg_info,
                        'package_type': package_type,
                        'first_detected': data.get('first_detected', ''),
                        'scanner': data.get('scanner', ''),
                        'scanner_severity': data.get('scanner_severity', ''),
                        'cvss_v3_score': data.get('cvss_v3_score', ''),
                        'cvss_vectors': data.get('cvss_vectors', ''),
                        'originally_published': data.get('originally_published', ''),
                        'last_modified': data.get('last_modified', ''),
                        'description': data.get('description', ''),
                        'references': data.get('references', ''),
                        'full_threatalert_data': data.get('full_text', ''),
                        'engineering_report': eng_report
                    })

if __name__ == "__main__":
    main()