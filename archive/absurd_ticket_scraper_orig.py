#!/usr/bin/env python3

import sys
import subprocess
import csv
from datetime import datetime

def load_search_strings(filename):
    """Load search strings from a file, one per line"""
    with open(filename, 'r') as f:
        return [line.strip() for line in f if line.strip()]

def run_glab_command(issue_id, repo):
    """Run glab command and return output"""
    try:
        result = subprocess.run([
            'glab', 'issue', 'view', str(issue_id), 
            '--comments', '--repo', repo
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if result.returncode != 0:
            return ""
        
        return result.stdout
    except Exception:
        return ""

def write_csv_results(search_strings, string_results, output_file):
    """Write results to CSV file showing which search strings appear in which issues"""
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['search_string', 'issue_id', 'fix_status']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        
        for search_string in search_strings:
            if search_string in string_results:
                # Write Fix Available entries
                for issue_id, _ in string_results[search_string]['fix_available']:
                    writer.writerow({
                        'search_string': search_string,
                        'issue_id': issue_id,
                        'fix_status': 'Fix Available'
                    })
                
                # Write VD entries
                for issue_id, _ in string_results[search_string]['vd']:
                    writer.writerow({
                        'search_string': search_string,
                        'issue_id': issue_id,
                        'fix_status': 'VD'
                    })

def main():
    if len(sys.argv) not in [3, 4]:
        print("Usage: python script.py <csv_file> <search_strings_file> [output_csv]")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    search_strings_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) == 4 else f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    repo = "threatalert/axonius-federal"
    
    # Load search strings from file
    search_strings = load_search_strings(search_strings_file)
    print(f"Loaded {len(search_strings)} search strings")
    
    string_results = {}  # Track results per search string
    
    with open(csv_file, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # Skip header
        
        for row in reader:
            if not row:
                continue
            
            issue_id = row[0].strip()
            print(f"=== Issue #{issue_id} ===")
            
            # Get issue content once per issue
            issue_content = run_glab_command(issue_id, repo)
            
            if not issue_content:
                print(f"Could not retrieve issue #{issue_id}")
                continue
            
            found_strings = []
            issue_has_fix = "fixed version:" in issue_content.lower()
            
            # Check each search string
            for search_string in search_strings:
                if search_string.lower() in issue_content.lower():
                    found_strings.append(search_string)
                    
                    # Initialize search string tracking
                    if search_string not in string_results:
                        string_results[search_string] = {'fix_available': [], 'vd': []}
                    
                    # Find last matching line for this string
                    matching_lines = [line.strip() for line in issue_content.split('\n') 
                                    if search_string.lower() in line.lower()]
                    last_match = matching_lines[-1] if matching_lines else ""
                    
                    # Categorize by fix availability
                    if issue_has_fix:
                        string_results[search_string]['fix_available'].append((issue_id, last_match))
                    else:
                        string_results[search_string]['vd'].append((issue_id, last_match))
                    
                    print(f"✓ Found '{search_string}': {last_match}")
            
            if found_strings:
                status = "Fix Available" if issue_has_fix else "VD"
                print(f"  → {status} ({len(found_strings)} matches)")
            else:
                print("✗ No matches found")
            
            print()
    
    # Write results to CSV
    write_csv_results(search_strings, string_results, output_file)
    print(f"\nResults written to: {output_file}")
    
    # Console summary
    print("\n" + "=" * 70)
    print("SUMMARY:")
    print("=" * 70)
    
    total_matches = 0
    strings_with_matches = 0
    
    for search_string in search_strings:
        if search_string in string_results:
            fix_count = len(string_results[search_string]['fix_available'])
            vd_count = len(string_results[search_string]['vd'])
            total_count = fix_count + vd_count
            total_matches += total_count
            strings_with_matches += 1
            
            print(f"'{search_string}': {total_count} matches ({fix_count} fix available, {vd_count} VD)")
        else:
            print(f"'{search_string}': No matches")
    
    print(f"\nTotal matches across all strings: {total_matches}")
    print(f"Search strings with matches: {strings_with_matches}/{len(search_strings)}")

if __name__ == "__main__":
    main()