# gitlab-utils

A collection of GitLab utilities for vulnerability management, issue tracking, and bulk operations.

## Bulk Commenter Scripts

These scripts allow for bulk commenting on GitLab issues:

- `bulker_kernel_fp.sh` - Makes bulk False Positive comments
- `bulker_fp_approval.sh` - Makes bulk False Positive approval comments  
- `bulker_dr_submitted.sh` - Makes bulk DR submittal comments
- `bulker_dr_submitted_date.sh` - Makes bulk DR submittal comments with dates
- `bulker_dr_submitted_for_sponsor_review.sh` - Makes bulk DR sponsor review comments
- `bulker_commenter.sh` - General purpose bulk commenting
- `bulker_cvss.sh` - Bulk CVSS scoring comments
- `bulker_remove_label.sh` - Removes labels in bulk
- `bulker_vd.sh` - Makes bulk VD (Vendor Disposition) comments
- `bulker_vd-ra.sh` - Makes bulk VD-RA comments

### Usage:
```bash
./script_name.sh csvfile.csv
```

### Example:
```bash
./bulker_commenter.sh issue_ids.csv
```

### CSV Format:
The CSV file should contain issue IDs in the following format:
```
ID
NUM1
NUM2
NUM3
...
NUMN
```
**Note:** An empty line is required at the end of the CSV file.

## Data Collection and Reporting Tools

### absurd_data_collector.py
Collects vulnerability data from GitLab for analysis and reporting.

### absurd_report_creation_v2.py  
Creates comprehensive vulnerability reports from collected data, generating Excel files with analysis and metrics.

# Step 1: Collect GitLab data
Example:
**Usage Example:**
```bash
python3 absurd_data_collector.py ISSUES_LIST.csv SEARCH_STRINGS.txt
```

# Step 2: Generate reports with CVE paths
**Usage Example:**
```bash
python3 absurd_report_creation_v4.py COLECTED_GITLAB_DATA.json ecr_findings_with_filepaths.csv
```

This tool takes an Issue List and searches repositories for the provided issue IDs, creating a filterable CSV with Issue IDs, repository names, and fix status (Fix Available or VD).

## Archive

Historical versions of scripts are maintained in the `archive/` directory for reference and rollback purposes.
