#!/bin/bash

# GitLab Issue Batch Comment Script
# This script reads IDs from column C of a CSV file and adds comments to GitLab issues

# Check if CSV file is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <csv_file>"
    echo "Example: $0 issues.csv"
    exit 1
fi

CSV_FILE="$1"

# Check if file exists
if [ ! -f "$CSV_FILE" ]; then
    echo "Error: File '$CSV_FILE' not found!"
    exit 1
fi

# Convert Windows line endings to Unix format
echo "Converting file format (removing Windows line endings)..."
dos2unix "$CSV_FILE" 2>/dev/null || {
    echo "Note: dos2unix not found, continuing without conversion"
}

# Repository configuration
REPO="threatalert/axonius-federal"

# Function to generate comment message with package name
generate_comment_message() {
    local package_name="$1"
    cat << EOF
### Overall Remediation Plan
Await a version unaffected by this vulnerability. Until such time, mitigate risk associated with this vulnerability via environmental controls.

### Deviation Rationale
No remediation is currently possible from the vendor. The system is not exposed externally. In order to obtain access to the system, host-level access would be required, which sits behind tightly-controlled MFA. Continuous logging and monitoring activities would also trigger incident response.

### Weakness Source Identifier
ThreatAlert

### Vendor Dependent Product Name
$package_name

### Vendor Check In
Current package is the latest available.

### Attack Complexity (MAC) Explanation
MFA and local access

### Attack Vector (MAV) Explanation
Access to platform systems behind AWS System Manager and MFA

### Availability Impact (MA) Explanation
Regular backups are performed. If a successful attack caused availability or resource issues, the instance could be restored fully from a backup.

### Confidentiality Impact (MC) Explanation
The attacker does not have control over the kind of information obtained through this exploitation.

### Integrity Impact (MI) Explanation
The attacker does not have control over the kind of information obtained through this exploitation.

### Privileges Required (MPR) Explanation
MFA and local access

### Remediation Level (RL) Explanation
The system is not exposed externally. In order to obtain access to the system, host-level access would be required, which sits behind tightly-controlled MFA. Continuous logging and monitoring activities would also trigger incident response.

### User Interaction (MUI) Explanation
Local access via AWS Systems Manager

/label ~"POA&M" ~"DeviationRequest::Required" ~"ISSM::ReviewRequired" ~"Vendor Dependency" ~"AdjustedRisk::Low"
EOF
}

echo "Processing CSV file: $CSV_FILE"
echo "Repository: $REPO"
echo "----------------------------------------"

# Counter variables
processed=0
errors=0

# Read CSV file and extract column A (assuming comma-separated)
# Skip header row if present
while IFS=',' read -r issue_id rest; do
    # Remove quotes, whitespace, and any carriage returns from issue_id
    issue_id=$(echo "$issue_id" | sed 's/^"//;s/"$//' | tr -d '\r\n' | xargs)

    # Skip empty IDs
    if [ -z "$issue_id" ] || [ "$issue_id" = "" ]; then
        echo "Skipping empty ID in row"
        continue
    fi
    
    # Check if issue_id is numeric
    if ! [[ "$issue_id" =~ ^[0-9]+$ ]]; then
        echo "Skipping non-numeric ID: '$issue_id'"
        continue
    fi
    
    echo "Processing Issue ID: $issue_id"
    
    # Get Package Name
    package_name=$(glab issue view "$issue_id" --repo "$REPO" | grep Pkg | sed 's/&#x27;/'"'"'/g' | grep -o "'PkgName': '[^']*'" | cut -d"'" -f4)
    
    if [ -z "$package_name" ]; then
        echo "Warning: Could not extract package name for issue #$issue_id"
        package_name="Unknown Package"
    fi
    
    echo "Package Name: $package_name"
    
    # Generate comment message with the package name
    COMMENT_MESSAGE=$(generate_comment_message "$package_name")

    # Run glab command
    if glab issue note "$issue_id" -m "$COMMENT_MESSAGE" --repo "$REPO"; then
        echo "✓ Successfully added comment to issue #$issue_id"
        ((processed++))
    else
        echo "✗ Failed to add comment to issue #$issue_id"
        ((errors++))
    fi
    
    echo "----------------------------------------"
    
    # Optional: Add a small delay to avoid overwhelming the server
    sleep 1
done < <(tail -n +2 "$CSV_FILE")

echo "Processing complete!"
echo "Successfully processed: $processed issues"
echo "Errors: $errors issues"