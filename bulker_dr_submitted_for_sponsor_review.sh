#!/bin/bash

# GitLab Issue Batch Comment Script
# This script reads IDs from column C of a CSV file and adds comments to GitLab issues

# Check if CSV file is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <csv_file>"
    echo "Example: $0 issues.csv"
    exit 1
fi

CSV_FILE="$1"

# Check if file exists
if [ ! -f "$CSV_FILE" ]; then
    echo "Error: File '$CSV_FILE' not found!"
    exit 1
fi

# Convert Windows line endings to Unix format
echo "Converting file format (removing Windows line endings)..."
dos2unix "$CSV_FILE" 2>/dev/null || {
    echo "Note: dos2unix not found, continuing without conversion"
}

# Repository configuration
REPO="threatalert/axonius-federal"

# Comment message
COMMENT_MESSAGE='Submitting for Sponsor review on DR Form.
/label ~"DeviationRequest::Submitted"'

echo "Processing CSV file: $CSV_FILE"
echo "Repository: $REPO"
echo "----------------------------------------"

# Counter files for tracking across subshell
temp_dir=$(mktemp -d)
processed_file="$temp_dir/processed"
errors_file="$temp_dir/errors"
echo "0" > "$processed_file"
echo "0" > "$errors_file"

# Read CSV file and extract column A (assuming comma-separated)
# Skip header row if present
tail -n +2 "$CSV_FILE" | while IFS=',' read -r issue_id rest; do
    # Remove quotes, whitespace, and any carriage returns from issue_id
    issue_id=$(echo "$issue_id" | sed 's/^"//;s/"$//' | tr -d '\r\n' | xargs)
    
    # Skip empty IDs
    if [ -z "$issue_id" ] || [ "$issue_id" = "" ]; then
        echo "Skipping empty ID in row"
        continue
    fi
    
    # Check if issue_id is numeric
    if ! [[ "$issue_id" =~ ^[0-9]+$ ]]; then
        echo "Skipping non-numeric ID: '$issue_id'"
        continue
    fi
    
    echo "Processing Issue ID: $issue_id"
    
    # Run glab command
    if glab issue note "$issue_id" -m "$COMMENT_MESSAGE" --repo "$REPO"; then
        echo "✓ Successfully added comment to issue #$issue_id"
        echo $(($(cat "$processed_file") + 1)) > "$processed_file"
    else
        echo "✗ Failed to add comment to issue #$issue_id"
        echo $(($(cat "$errors_file") + 1)) > "$errors_file"
    fi
    
    echo "----------------------------------------"
    
    # Optional: Add a small delay to avoid overwhelming the server
    sleep 1
done

# Read final counts
processed=$(cat "$processed_file")
errors=$(cat "$errors_file")

# Cleanup temp files
rm -rf "$temp_dir"

echo "Processing complete!"
echo "Successfully processed: $processed issues"
echo "Errors: $errors issues"